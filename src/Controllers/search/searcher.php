<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

$search = $_POST["query"];
$fts_query = preg_replace('/\s+/', ' & ', $search);
$like_query = '%' . addcslashes($search, '%_') . '%';

$sql = "SELECT type, id, title,
       ts_rank_cd(searchable, query) AS rank
FROM global_search, to_tsquery('simple', '$fts_query') query
WHERE searchable @@ query
   OR title ILIKE '$like_query'
ORDER BY rank DESC
LIMIT 20";

echo $sql;

$results = Connection::getDataFromDatabase($sql, defaultDB)[1];
print_r($results);
